import type { Renderer } from "@/base/renderer";
import heroPath from "@/sprites/hero.png";
import { Entity } from ".";

export class Player extends Entity {
  facingDirection = 0; // down, left, right, up

  getSpriteId() {
    return { path: heroPath, index: this.facingDirection };
  }

  render(renderer: Renderer, tileX: number, tileY: number, _: number) {
    renderer.drawSprite(heroPath, this.facingDirection, tileX, tileY, 0);
  }
}

export class PlayerController {}
