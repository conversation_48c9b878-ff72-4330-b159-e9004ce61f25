(function(){const t=document.createElement("link").relList;if(t&&t.supports&&t.supports("modulepreload"))return;for(const e of document.querySelectorAll('link[rel="modulepreload"]'))i(e);new MutationObserver(e=>{for(const r of e)if(r.type==="childList")for(const n of r.addedNodes)n.tagName==="LINK"&&n.rel==="modulepreload"&&i(n)}).observe(document,{childList:!0,subtree:!0});function s(e){const r={};return e.integrity&&(r.integrity=e.integrity),e.referrerPolicy&&(r.referrerPolicy=e.referrerPolicy),e.crossOrigin==="use-credentials"?r.credentials="include":e.crossOrigin==="anonymous"?r.credentials="omit":r.credentials="same-origin",r}function i(e){if(e.ep)return;e.ep=!0;const r=s(e);fetch(e.href,r)}})();class u{map=[];ground;pos={x:0,y:0};constructor(t){this.ground=t}}const B="/assets/enemies-BJ-7wTqn.png",p="data:image/png;base64,iVBORw0KGgoAAAANSUhEUgAAACAAAAAgCAIAAAD8GO2jAAAACXBIWXMAAAsTAAALEwEAmpwYAAAABGdBTUEAALGOfPtRkwAAACBjSFJNAAB6JQAAgIMAAPn/AACA6QAAdTAAAOpgAAA6mAAAF2+SX8VGAAACp0lEQVR42mI0MjFiY2H79eeXvLz8w4cP5WXkHz55CGEDxRkYGCQlJIEiEDYcgNTLyIMYf3+9ff0WISgv/+sXVATIBeoCCCAmkBCG6UBFQDZQHIhAImC2sKgwkATqBDKAOoHiQPbz588hIlBD7j1kY2aDKAZygSRAADFaWFjALX/+9DnQvSBtL57DnQAUAXJB4swMDH8RnoCoYfjDICkview4iBqIFiAJEECMgcGBmCEDdc7ThxAjkMWBAhAGnAsEQO1QR0DC6u8voD8gIgABxGhiYsKAAaSkpJ49ewYhGfAC/CqB4gABxARnIZNY9cBlIQwIgKiBq0Q2ByIOEEBMyCbCRdFEMNVgMtDUwN0BEEBMcBORNeA3HZeJWNUABBDCB5ihgd90ZL+isZEBQAAxEYxPrLIQLn4fQNQABBATVlPQog6rLLL/cKkBigAEEKOfnx8uPfhTIaZjsSZTgABiQrYN2UQIQDMdT0qFK0BOqUApgABi5uXlRXMpsmagLKZvMGPo8+fPyH6C6IKIAwQQEx7T0dIrZljh1wVRCRBATMhRijWscdmBbDpyokAzByCAsJdFmLmJgSwA1AsQQEyYSQItPtFiGDO24QCrXoAAYsJMOZg5iJhSAVMWQgIEEDSI8Jd0yPkWf4rClAUIICZcJQ9m6QT3JWZpiNUHEHGAAELxAVqWwXQXpp+wpmBkOwACCD0V4amb0NIrkQkJIICYMOMda6rADBM0ZWgpAq4MIICY8OdJrLGClpmxlgVwEiCAUIIIf/gQmbPQHAEQQEy40hly0YhVHDNnYbUJIICY0IpZ/PU7ZrlEMN8BBBATkTUwrnYGZgmIJgsQQEzE1MB4ymqspQiyLEAAMWEmaly1K2aZTEwZDhBAoDoZaw7CWo7iz+pYAUCAAQC/UaXtgb5eBAAAAABJRU5ErkJggg==",m="/assets/hero-88XNLfAw.png",w="/assets/items-BrHk4M-b.png",E="/assets/npcs-BkY_0sBL.png",Q="/assets/terrains-D8LPTSyI.png",o=32,d=13,A=2;async function S(a){return new Promise((t,s)=>{const i=new Image;i.onload=()=>t(i),i.onerror=s,i.src=a})}async function y(a,t){const{width:s,height:i}=t instanceof Object?t:{width:t,height:t},e=await S(a),r=[];for(let n=0;n<e.height/i;n++)r.push({startX:0,startY:n*i,width:s,height:i,numFrames:e.width/s});return{image:e,sprites:r}}class P{mainCanvas;mainCtx;animationSpeed;isInitialized=!1;intializePromise=null;_animationStartTime=0;spriteSheets={};constructor(t){const{mainCanvas:s,animationSpeed:i}={animationSpeed:300,...t};this.mainCanvas=s,this.mainCanvas.width=d*o*A,this.mainCanvas.height=d*o*A,this.mainCtx=this.mainCanvas.getContext("2d"),this.mainCtx.scale(A,A),this.animationSpeed=i,this.init([{path:m,size:{width:32,height:48}},{path:p,size:32},{path:w,size:32},{path:E,size:32},{path:Q,size:32},{path:B,size:32}])}async _init(t){for(const{path:s,size:i}of t)this.spriteSheets[s]=await y(s,i);this.isInitialized=!0}init(t){return this.intializePromise||(this.intializePromise=this._init(t)),this.intializePromise}drawSprite(t,s,i,e,r){const n=this.spriteSheets[t].sprites[s];if(!n)return;const c=r%n.numFrames,f=i*o,C=n.height===32?e*o:e*o-(n.height-o);this.mainCtx.drawImage(this.spriteSheets[t].image,n.startX+c*n.width,n.startY,n.width,n.height,f,C,n.width,n.height)}render(t){this.mainCtx.imageSmoothingEnabled=!1,this.mainCtx.clearRect(0,0,this.mainCanvas.width,this.mainCanvas.height);const s=Math.floor((Date.now()-this._animationStartTime)/this.animationSpeed);for(let i=0;i<t.map.length;i++)for(let e=0;e<t.map[i].length;e++)t.ground.render(this,e,i,s),t.map[i][e]?.render(this,e,i,s);requestAnimationFrame(()=>{this.render(t)})}}class l{getSpriteId(){}render(t,s,i,e){const r=this.getSpriteId();if(!r)return;const{path:n,index:c}=r;t.drawSprite(n,c,s,i,e)}}class D extends l{facingDirection=0;getSpriteId(){return{path:m,index:this.facingDirection}}render(t,s,i,e){t.drawSprite(m,this.facingDirection,s,i,0)}}class L extends l{getSpriteId(){return{path:p,index:0}}}const h=[];for(let a=0;a<13;a++){h.push([]);for(let t=0;t<13;t++)h[a].push(null)}const M=new D;h[6][6]=M;const x=new L,I=new u(x);I.map=h;class T{renderer;constructor(){this.renderer=new P({mainCanvas:document.getElementById("mainCanvas")})}}const g=new T;g.renderer.intializePromise?.then(()=>{g.renderer.render(I)});
