import type { Renderable, Renderer } from "@/base/renderer";

export class Entity implements Renderable {
  /**
   * sprite will be rendered if this function is implemented
   */
  getSpriteId(): { path: string; index: number } | void {}

  render(
    renderer: Renderer,
    tileX: number,
    tileY: number,
    animationFrame: number
  ) {
    const spriteId = this.getSpriteId();
    if (!spriteId) {
      return;
    }
    const { path, index } = spriteId;
    renderer.drawSprite(path, index, tileX, tileY, animationFrame);
  }
}
