import { State } from "@/state";

import enemiesPath from "@/sprites/enemies.png";
import groundPath from "@/sprites/ground.png";
import heroPath from "@/sprites/hero.png";
import itemsPath from "@/sprites/items.png";
import npcsPath from "@/sprites/npcs.png";
import terrainsPath from "@/sprites/terrains.png";

const TILE_PIXEL_SIZE = 32;

const MAIN_CANVAS_VISIBLE_TILE_NUM = 13;

const SCALE = 2;

async function loadImage(path: string) {
  return new Promise<HTMLImageElement>((resolve, reject) => {
    const img = new Image();
    img.onload = () => resolve(img);
    img.onerror = reject;
    img.src = path;
  });
}

export type Size = number | { width: number; height: number };

interface SpriteSheet {
  image: HTMLImageElement;
  sprites: {
    startX: number;
    startY: number;
    width: number;
    height: number;
    numFrames: number;
  }[];
}

async function loadSpriteSheet(path: string, size: Size): Promise<SpriteSheet> {
  const { width: spriteWidth, height: spriteHeight } =
    size instanceof Object ? size : { width: size, height: size };

  const image = await loadImage(path);

  const sprites = [];

  for (let i = 0; i < image.height / spriteHeight; i++) {
    sprites.push({
      startX: 0,
      startY: i * spriteHeight,
      width: spriteWidth,
      height: spriteHeight,
      numFrames: image.width / spriteWidth,
    });
  }

  return {
    image,
    sprites,
  };
}

export interface RendererOptions {
  mainCanvas: HTMLCanvasElement;

  animationSpeed?: number;
}

export class Renderer {
  mainCanvas: HTMLCanvasElement;
  mainCtx: CanvasRenderingContext2D;

  animationSpeed: number;

  isInitialized = false;
  intializePromise: Promise<void> | null = null;

  _animationStartTime: number = 0;

  /**
   * array of sprite sheets
   * order is the same as sprite in the image
   * each sprite sheet is an array of ImageData representing the frames
   */
  spriteSheets: {
    [key: string]: SpriteSheet;
  } = {};

  constructor(settings: RendererOptions) {
    const { mainCanvas, animationSpeed } = { animationSpeed: 300, ...settings };

    this.mainCanvas = mainCanvas;
    this.mainCanvas.width =
      MAIN_CANVAS_VISIBLE_TILE_NUM * TILE_PIXEL_SIZE * SCALE;
    this.mainCanvas.height =
      MAIN_CANVAS_VISIBLE_TILE_NUM * TILE_PIXEL_SIZE * SCALE;
    this.mainCtx = this.mainCanvas.getContext("2d")!;
    this.mainCtx.scale(SCALE, SCALE);

    this.animationSpeed = animationSpeed;

    this.init([
      { path: heroPath, size: { width: 32, height: 48 } },
      { path: groundPath, size: 32 },
      { path: itemsPath, size: 32 },
      { path: npcsPath, size: 32 },
      { path: terrainsPath, size: 32 },
      { path: enemiesPath, size: 32 },
    ]);
  }

  async _init(
    spriteConfigs: {
      path: string;
      size: Size;
    }[]
  ) {
    for (const { path, size } of spriteConfigs) {
      this.spriteSheets[path] = await loadSpriteSheet(path, size);
    }
    this.isInitialized = true;
  }

  init(spriteConfigs: { path: string; size: Size }[]) {
    if (!this.intializePromise) {
      this.intializePromise = this._init(spriteConfigs);
    }
    return this.intializePromise;
  }

  drawSprite(
    path: string,
    index: number,
    tileX: number,
    tileY: number,
    animationFrame: number
  ) {
    const sprite = this.spriteSheets[path].sprites[index];
    if (!sprite) {
      return;
    }

    const frame = animationFrame % sprite.numFrames;
    const x = tileX * TILE_PIXEL_SIZE;
    const y =
      sprite.height === 32
        ? tileY * TILE_PIXEL_SIZE
        : tileY * TILE_PIXEL_SIZE - (sprite.height - TILE_PIXEL_SIZE);

    this.mainCtx.drawImage(
      this.spriteSheets[path].image,
      sprite.startX + frame * sprite.width,
      sprite.startY,
      sprite.width,
      sprite.height,
      x,
      y,
      sprite.width,
      sprite.height
    );
  }

  render(state: State) {
    this.mainCtx.imageSmoothingEnabled = false;

    this.mainCtx.clearRect(0, 0, this.mainCanvas.width, this.mainCanvas.height);

    const animationFrame = Math.floor(
      (Date.now() - this._animationStartTime) / this.animationSpeed
    );

    // // calculate the visible area around the player position
    // const halfTileSize = Math.floor((MAIN_CANVAS_TILE_SIZE - 1) / 2);
    // const startX = state.pos.x - halfTileSize;
    // const startY = state.pos.y - halfTileSize;

    // // render each visible tile
    // for (let canvasY = 0; canvasY < MAIN_CANVAS_TILE_SIZE; canvasY++) {
    //   const tileY = startY + canvasY;
    //   if (tileY < 0 || tileY >= state.map.length) {
    //     continue;
    //   }

    //   for (let canvasX = 0; canvasX < MAIN_CANVAS_TILE_SIZE; canvasX++) {
    //     const tileX = startX + canvasX;
    //     if (tileX < 0 || tileX >= state.map[tileY].length) {
    //       continue;
    //     }

    //     state.map[tileY][tileX].render(this, tileX, tileY, animationFrame);
    //   }
    // }

    for (let y = 0; y < state.map.length; y++) {
      for (let x = 0; x < state.map[y].length; x++) {
        state.ground.render(this, x, y, animationFrame);
        state.map[y][x]?.render(this, x, y, animationFrame);
      }
    }

    requestAnimationFrame(() => {
      this.render(state);
    });
  }
}

export interface Renderable {
  render(
    renderer: Renderer,
    tileX: number,
    tileY: number,
    animationFrame: number
  ): void;
}
