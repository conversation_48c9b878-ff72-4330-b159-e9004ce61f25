import { Renderer } from "@/base/renderer";
import type { Entity } from "@/entity";
import { Player } from "@/entity/player";
import { Ground } from "@/entity/environment";
import { State } from "@/state";

const map: (Entity | null)[][] = [];
for (let y = 0; y < 13; y++) {
  map.push([]);
  for (let x = 0; x < 13; x++) {
    map[y].push(null);
  }
}

const player = new Player();
map[6][6] = player;

const ground = new Ground();
const state = new State(ground);
state.map = map;

export class Game {
  renderer: Renderer;

  constructor() {
    this.renderer = new Renderer({
      mainCanvas: document.getElementById("mainCanvas") as HTMLCanvasElement,
    });
  }
}

const game = new Game();
game.renderer.intializePromise?.then(() => {
  game.renderer.render(state);
});
